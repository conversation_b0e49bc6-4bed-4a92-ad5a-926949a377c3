#!/usr/bin/env python3
"""
测试Qwen3:30B MoE模型连接和性能
"""

import os
import asyncio
import time
from dotenv import load_dotenv
from openai import AsyncOpenAI

load_dotenv()

async def test_qwen3_moe():
    """测试Qwen3 MoE模型连接和响应"""
    
    # 读取配置
    base_url = os.getenv("OPENAI_API_BASE", "http://localhost:11435/v1")
    api_key = os.getenv("OPENAI_API_KEY", "local-key")
    model = os.getenv("OPENAI_MODEL", "qwen3:30b")
    
    print("🧪 测试Qwen3:30B MoE模型")
    print("=" * 50)
    print(f"🤖 模型: {model}")
    print(f"🔗 API地址: {base_url}")
    print(f"🔑 API密钥: {api_key}")
    print()
    
    try:
        # 创建客户端
        client = AsyncOpenAI(
            base_url=base_url,
            api_key=api_key,
        )
        
        print("📡 正在连接Qwen3 MoE模型...")
        
        # 测试1: 简单对话
        print("🧪 测试1: 基础对话能力")
        start_time = time.time()
        
        response = await client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "你是一个AI助手，请简洁回答问题。"},
                {"role": "user", "content": "你好，请简单介绍一下Qwen3 MoE模型的特点。"}
            ],
            max_tokens=200,
            temperature=0.7
        )
        
        end_time = time.time()
        answer = response.choices[0].message.content
        print(f"✅ 响应时间: {end_time - start_time:.2f}秒")
        print(f"🤖 模型回复: {answer}")
        print()
        
        # 测试2: JSON格式输出
        print("🧪 测试2: JSON格式输出能力")
        start_time = time.time()
        
        json_response = await client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "请严格按照JSON格式回答，格式：{\"tool\": \"工具名\", \"params\": {参数}}。不要添加任何其他内容。"},
                {"role": "user", "content": "我想查询游戏状态"}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        end_time = time.time()
        json_answer = json_response.choices[0].message.content
        print(f"✅ 响应时间: {end_time - start_time:.2f}秒")
        print(f"📋 JSON回复: {json_answer}")
        print()
        
        # 测试3: 复杂推理
        print("🧪 测试3: 复杂推理能力")
        start_time = time.time()
        
        reasoning_response = await client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "你是游戏策略专家，请分析并给出建议。"},
                {"role": "user", "content": "在红警游戏中，我有15000金钱，200电力，需要快速扩张经济。请给出最优的建造顺序。"}
            ],
            max_tokens=300,
            temperature=0.3
        )
        
        end_time = time.time()
        reasoning_answer = reasoning_response.choices[0].message.content
        print(f"✅ 响应时间: {end_time - start_time:.2f}秒")
        print(f"🧠 策略建议: {reasoning_answer}")
        print()
        
        print("🎉 Qwen3:30B MoE模型测试完成！")
        print("💡 模型表现优秀，可以用于MCP智能体")
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print()
        print("🔧 可能的原因:")
        print("1. 模型还在下载中，请等待下载完成")
        print("2. Ollama服务未在端口11435运行")
        print("3. 模型名称不正确")
        print("4. 检查命令: OLLAMA_HOST=127.0.0.1:11435 ollama list")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(test_qwen3_moe())
